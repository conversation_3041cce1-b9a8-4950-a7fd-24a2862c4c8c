import React, { useState } from 'react';
import { Button, Form, Input, Select, message } from 'antd';
import { ModalForm } from '@ant-design/pro-components';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { TodoService } from '@/services/todo';
import type { TodoResponse } from '@/types/api';

/**
 * 简化的TODO测试组件，用于验证添加和编辑功能
 */
const TodoTest: React.FC = () => {
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [todos, setTodos] = useState<TodoResponse[]>([]);

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    setSubmitting(true);
    try {
      if (editingId) {
        // 编辑模式
        const updated = await TodoService.updateTodo(editingId, values);
        setTodos(todos.map(todo => todo.id === editingId ? updated : todo));
        message.success('任务更新成功');
      } else {
        // 添加模式
        const newTodo = await TodoService.createTodo(values);
        setTodos([newTodo, ...todos]);
        message.success('任务创建成功');
      }
      return true; // 关闭模态框
    } catch (error: any) {
      message.error(error?.response?.data?.message || '操作失败');
      return false; // 保持模态框打开
    } finally {
      setSubmitting(false);
    }
  };

  // 打开添加模态框
  const openAddModal = () => {
    setEditingId(null);
    form.resetFields();
    form.setFieldsValue({ priority: 2 });
    setModalVisible(true);
  };

  // 打开编辑模态框
  const openEditModal = (todo: TodoResponse) => {
    setEditingId(todo.id);
    form.setFieldsValue({
      title: todo.title,
      description: todo.description || '',
      priority: todo.priority,
    });
    setModalVisible(true);
  };

  // 获取TODO列表
  const fetchTodos = async () => {
    try {
      const data = await TodoService.getUserTodos();
      setTodos(data);
      message.success('数据加载成功');
    } catch (error: any) {
      message.error('加载失败: ' + (error?.response?.data?.message || error.message));
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <h2>TODO功能测试</h2>
      
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={openAddModal}>
          添加任务
        </Button>
        <Button style={{ marginLeft: 8 }} onClick={fetchTodos}>
          刷新列表
        </Button>
      </div>

      {/* 任务列表 */}
      <div>
        {todos.map(todo => (
          <div key={todo.id} style={{ 
            border: '1px solid #d9d9d9', 
            padding: 12, 
            marginBottom: 8, 
            borderRadius: 6,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div>
              <div style={{ fontWeight: 'bold' }}>{todo.title}</div>
              {todo.description && <div style={{ color: '#666' }}>{todo.description}</div>}
              <div style={{ fontSize: 12, color: '#999' }}>
                优先级: {todo.priority === 3 ? '高' : todo.priority === 2 ? '中' : '低'} | 
                状态: {todo.status === 1 ? '已完成' : '未完成'}
              </div>
            </div>
            <Button 
              icon={<EditOutlined />} 
              onClick={() => openEditModal(todo)}
              size="small"
            >
              编辑
            </Button>
          </div>
        ))}
      </div>

      {/* 表单模态框 */}
      <ModalForm
        title={editingId ? '编辑任务' : '添加任务'}
        open={modalVisible}
        onOpenChange={(visible) => {
          if (!visible) {
            setEditingId(null);
            form.resetFields();
          }
          setModalVisible(visible);
        }}
        form={form}
        onFinish={handleSubmit}
        width={500}
        modalProps={{
          destroyOnClose: false,
          maskClosable: !submitting,
        }}
        submitter={{
          searchConfig: {
            submitText: editingId ? '更新' : '创建',
            resetText: '取消',
          },
          submitButtonProps: {
            loading: submitting,
          },
          onReset: () => setModalVisible(false),
        }}
      >
        <Form.Item
          name="title"
          label="任务名称"
          rules={[
            { required: true, message: '请输入任务名称' },
            { max: 100, message: '任务名称不能超过100个字符' },
            {
              validator: (_, value) => {
                if (!value || !value.trim()) {
                  return Promise.reject(new Error('任务名称不能为空白字符'));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input placeholder="请输入任务名称" maxLength={100} showCount />
        </Form.Item>

        <Form.Item
          name="description"
          label="任务描述"
          rules={[{ max: 500, message: '任务描述不能超过500个字符' }]}
        >
          <Input.TextArea 
            placeholder="请输入任务描述（可选）" 
            maxLength={500} 
            showCount 
            rows={3} 
          />
        </Form.Item>

        <Form.Item
          name="priority"
          label="优先级"
          rules={[{ required: true, message: '请选择优先级' }]}
        >
          <Select
            options={[
              { value: 3, label: '高优先级' },
              { value: 2, label: '中优先级' },
              { value: 1, label: '低优先级' },
            ]}
          />
        </Form.Item>
      </ModalForm>
    </div>
  );
};

export default TodoTest;
