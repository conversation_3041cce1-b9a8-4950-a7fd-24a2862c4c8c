import { TodoService } from '@/services/todo';

// Mock the TodoService
jest.mock('@/services/todo');
const mockTodoService = TodoService as jest.Mocked<typeof TodoService>;

// Mock data
const mockTodos = [
  {
    id: 1,
    title: '测试任务1',
    description: '这是一个测试任务',
    status: 0,
    priority: 2,
    userId: 1,
    createdAt: '2024-01-01 10:00:00',
    updatedAt: '2024-01-01 10:00:00',
  },
  {
    id: 2,
    title: '测试任务2',
    description: '',
    status: 1,
    priority: 3,
    userId: 1,
    createdAt: '2024-01-01 11:00:00',
    updatedAt: '2024-01-01 11:00:00',
  },
];

const mockStats = {
  highPriorityCount: 1,
  mediumPriorityCount: 1,
  lowPriorityCount: 0,
  totalCount: 2,
  completedCount: 1,
  completionPercentage: 50,
};

describe('TodoService API Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('应该能够创建新任务', async () => {
    const newTodoRequest = {
      title: '新任务',
      description: '新任务描述',
      priority: 2,
    };

    const newTodoResponse = {
      id: 3,
      title: '新任务',
      description: '新任务描述',
      status: 0,
      priority: 2,
      userId: 1,
      createdAt: '2024-01-01 12:00:00',
      updatedAt: '2024-01-01 12:00:00',
    };

    mockTodoService.createTodo.mockResolvedValue(newTodoResponse);

    const result = await TodoService.createTodo(newTodoRequest);

    expect(mockTodoService.createTodo).toHaveBeenCalledWith(newTodoRequest);
    expect(result).toEqual(newTodoResponse);
  });

  test('应该能够更新现有任务', async () => {
    const updateRequest = {
      title: '更新的任务',
      description: '更新的描述',
      priority: 3,
    };

    const updatedTodoResponse = {
      id: 1,
      title: '更新的任务',
      description: '更新的描述',
      status: 0,
      priority: 3,
      userId: 1,
      createdAt: '2024-01-01 10:00:00',
      updatedAt: '2024-01-01 13:00:00',
    };

    mockTodoService.updateTodo.mockResolvedValue(updatedTodoResponse);

    const result = await TodoService.updateTodo(1, updateRequest);

    expect(mockTodoService.updateTodo).toHaveBeenCalledWith(1, updateRequest);
    expect(result).toEqual(updatedTodoResponse);
  });

  test('应该能够获取用户TODO列表', async () => {
    mockTodoService.getUserTodos.mockResolvedValue(mockTodos);

    const result = await TodoService.getUserTodos();

    expect(mockTodoService.getUserTodos).toHaveBeenCalled();
    expect(result).toEqual(mockTodos);
  });

  test('应该能够获取TODO统计信息', async () => {
    mockTodoService.getTodoStats.mockResolvedValue(mockStats);

    const result = await TodoService.getTodoStats();

    expect(mockTodoService.getTodoStats).toHaveBeenCalled();
    expect(result).toEqual(mockStats);
  });

  test('应该正确处理API错误', async () => {
    const errorMessage = '网络错误';
    mockTodoService.createTodo.mockRejectedValue(new Error(errorMessage));

    await expect(TodoService.createTodo({
      title: '测试任务',
      priority: 2,
    })).rejects.toThrow(errorMessage);
  });
});
