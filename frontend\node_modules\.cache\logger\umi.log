{"level":30,"time":1753946768910,"pid":18736,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果要支持低版本浏览器，可尝试新出的 legacy 配置项，详见 https://umijs.org/blog/legacy-browser\u001b[39m"}
{"level":30,"time":1753946768916,"pid":18736,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753946768917,"pid":18736,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753946768921,"pid":18736,"hostname":"DESKTOP-N6C2H1N","msg":"generate files"}
{"level":30,"time":1753946773168,"pid":18736,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":60,"time":1753946773723,"pid":18736,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Build failed with 1 error:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'","stack":"Error: Build failed with 1 error:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\n    at failureErrorWithLog (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"detail":{"details":"resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\n  using description file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\package.json (relative path: ./src)\n    using description file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\package.json (relative path: ./src/utils/devHelper)\n      no extension\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper doesn't exist\n      .json\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.json doesn't exist\n      .js\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.js doesn't exist\n      .jsx\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.jsx doesn't exist\n      .ts\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.ts doesn't exist\n      .tsx\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.tsx doesn't exist\n      .cjs\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.cjs doesn't exist\n      .mjs\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.mjs doesn't exist\n      as directory\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper doesn't exist"},"id":"","location":{"column":17,"file":"node_modules/enhanced-resolve/lib/Resolver.js","length":0,"line":309,"lineText":"\t\t\tconst error = new Error(\"Can't \" + message);\n    at finishWithoutResolve (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:309:18)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:386:15\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:15:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:27:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\DescriptionFilePlugin.js:87:43\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:15:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:16:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:27:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\DescriptionFilePlugin.js:87:43\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:16:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:15:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\DirectoryExistsPlugin.js:41:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","namespace":"file","suggestion":""},"notes":[{"location":{"column":16,"file":"node_modules/@umijs/preset-umi/dist/features/prepare/esbuildPlugins/esbuildAliasPlugin.js","length":9,"line":89,"lineText":"          build.onResolve({ filter: filter2 }, async (args) => {\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\prepare\\esbuildPlugins\\esbuildAliasPlugin.js:89:17\n    at Array.forEach (<anonymous>)\n    at setup (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\prepare\\esbuildPlugins\\esbuildAliasPlugin.js:75:10)\n    at handlePlugins (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1150:21)\n    at buildOrContextImpl (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:873:5)\n    at Object.buildOrContext (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:699:5)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:2023:15\n    at new Promise (<anonymous>)\n    at Object.build (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:2022:25)\n    at Object.build (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1873:51)\n    at build (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\prepare\\build.js:101:49)\n    at Hook.fn (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\prepare\\prepare.js:106:40)\n    at async H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\service\\service.js:184:15","namespace":"file","suggestion":""},"text":"This error came from the \"onResolve\" callback registered here:"},{"location":{"column":68,"file":"src/app.tsx","length":19,"line":11,"lineText":"import { setupDevVerificationCodeListener, showDevDebugPanel } from '@/utils/devHelper';","namespace":"","suggestion":""},"text":"The plugin \"esbuildAliasPlugin\" was triggered by this import"}],"pluginName":"esbuildAliasPlugin","text":"Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'"}],"warnings":[]},"msg":"Build failed with 1 error:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'"}
{"level":60,"time":1753946773948,"pid":18736,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753946773964,"pid":18736,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753946773981,"pid":18736,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1753946943627,"pid":21836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想预览构建后产物, 可尝试 max preview，详见 https://umijs.org/docs/api/commands#preview\u001b[39m"}
{"level":30,"time":1753946943632,"pid":21836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753946943632,"pid":21836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753946943634,"pid":21836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753946945687,"pid":21836,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":60,"time":1753946945985,"pid":21836,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Build failed with 1 error:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'","stack":"Error: Build failed with 1 error:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\n    at failureErrorWithLog (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"detail":{"details":"resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\n  using description file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\package.json (relative path: ./src)\n    using description file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\package.json (relative path: ./src/utils/devHelper)\n      no extension\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper doesn't exist\n      .json\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.json doesn't exist\n      .js\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.js doesn't exist\n      .jsx\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.jsx doesn't exist\n      .ts\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.ts doesn't exist\n      .tsx\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.tsx doesn't exist\n      .cjs\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.cjs doesn't exist\n      .mjs\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper.mjs doesn't exist\n      as directory\n        H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\utils\\devHelper doesn't exist"},"id":"","location":{"column":17,"file":"node_modules/enhanced-resolve/lib/Resolver.js","length":0,"line":309,"lineText":"\t\t\tconst error = new Error(\"Can't \" + message);\n    at finishWithoutResolve (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:309:18)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:386:15\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:15:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:27:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\DescriptionFilePlugin.js:87:43\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:15:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:16:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:27:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\DescriptionFilePlugin.js:87:43\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:16:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\Resolver.js:435:5\n    at eval (eval at create (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\tapable\\lib\\HookCodeFactory.js:33:10), <anonymous>:15:1)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\enhanced-resolve\\lib\\DirectoryExistsPlugin.js:41:15\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","namespace":"file","suggestion":""},"notes":[{"location":{"column":16,"file":"node_modules/@umijs/preset-umi/dist/features/prepare/esbuildPlugins/esbuildAliasPlugin.js","length":9,"line":89,"lineText":"          build.onResolve({ filter: filter2 }, async (args) => {\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\prepare\\esbuildPlugins\\esbuildAliasPlugin.js:89:17\n    at Array.forEach (<anonymous>)\n    at setup (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\prepare\\esbuildPlugins\\esbuildAliasPlugin.js:75:10)\n    at handlePlugins (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1150:21)\n    at buildOrContextImpl (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:873:5)\n    at Object.buildOrContext (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:699:5)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:2032:68\n    at new Promise (<anonymous>)\n    at Object.context (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:2032:27)\n    at Object.context (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1874:58)\n    at build (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\prepare\\build.js:96:46)\n    at Hook.fn (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\prepare\\prepare.js:106:40)\n    at async H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\service\\service.js:184:15","namespace":"file","suggestion":""},"text":"This error came from the \"onResolve\" callback registered here:"},{"location":{"column":68,"file":"src/app.tsx","length":19,"line":11,"lineText":"import { setupDevVerificationCodeListener, showDevDebugPanel } from '@/utils/devHelper';","namespace":"","suggestion":""},"text":"The plugin \"esbuildAliasPlugin\" was triggered by this import"}],"pluginName":"esbuildAliasPlugin","text":"Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'"}],"warnings":[]},"msg":"Build failed with 1 error:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/utils/devHelper' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'"}
{"level":60,"time":1753946945988,"pid":21836,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753946946005,"pid":21836,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753946946021,"pid":21836,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1753947010842,"pid":21436,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1753947010848,"pid":21436,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753947010848,"pid":21436,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753947010850,"pid":21436,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753947012876,"pid":21436,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753947350317,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局布局用 layout ，多层布局用 wrappers ，从文档了解更多路由的控制方法，详见 https://umijs.org/docs/guides/routes\u001b[39m"}
{"level":30,"time":1753947350322,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753947350323,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753947350327,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753947352389,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753947932385,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/utils/request.ts:219:3: ERROR: Unexpected \",\""}
{"level":50,"time":1753948003388,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\umi.ts"}
{"level":50,"time":1753948003514,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753948004062,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1753948005656,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753948006028,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1753948003274,"pid":14836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 编写 src/loading.(jsx|tsx) 可以自定义页面的加载动画。\u001b[39m"}
{"level":30,"time":1753948003279,"pid":14836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753948003279,"pid":14836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753948003281,"pid":14836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753948005525,"pid":14836,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":32,"time":1753948193609,"pid":14836,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":32,"time":1753948193553,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":32,"time":1753948941840,"pid":11428,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":30,"time":1753949256963,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g component 可以快速生成组件模板，详见 https://umijs.org/docs/guides/generator#组件生成器\u001b[39m"}
{"level":30,"time":1753949256970,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949256970,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949256972,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753949259180,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753949274170,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Memory Usage: 122.74 MB (RSS: 571.29 MB)"}
{"level":32,"time":1753949274252,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\index.html"}
{"level":32,"time":1753949274254,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\login\\index.html"}
{"level":32,"time":1753949274256,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Build dashboard\\index.html"}
{"level":32,"time":1753949274259,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Build team-management\\index.html"}
{"level":32,"time":1753949274261,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Build personal-center\\index.html"}
{"level":32,"time":1753949274263,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Build help\\index.html"}
{"level":32,"time":1753949274267,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Build test-interceptor\\index.html"}
{"level":32,"time":1753949274273,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Build index.html"}
{"level":32,"time":1753949274286,"pid":6548,"hostname":"DESKTOP-N6C2H1N","msg":"Build 404.html"}
{"level":30,"time":1753949343812,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局样式、全局脚本写在哪里？创建 src/global.(ts|css) 轻松解决，详见 https://umijs.org/docs/guides/directory-structure#globaljtsx\u001b[39m"}
{"level":30,"time":1753949343817,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949343818,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949343820,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753949345780,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753949500766,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\umi.ts"}
{"level":50,"time":1753949500884,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753949501311,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1753949500686,"pid":19884,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 想快速修改 html 模板、DIY 项目？试试编写项目级插件，详见 https://umijs.org/docs/guides/directory-structure#plugints\u001b[39m"}
{"level":30,"time":1753949500690,"pid":19884,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949500691,"pid":19884,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949500694,"pid":19884,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753949502751,"pid":19884,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753949502831,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753949503189,"pid":1164,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1753949824363,"pid":10120,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要增加 Git 提交消息校验和自动代码格式化, max g precommit 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#precommit-配置生成器\u001b[39m"}
{"level":30,"time":1753949824387,"pid":10120,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949824389,"pid":10120,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949824392,"pid":10120,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753949826487,"pid":10120,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753949828436,"pid":10120,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 11 errors:\nsrc/.umi/exports.ts:5:26: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core\\defineApp.ts\nsrc/.umi/exports.ts:8:57: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-access\\index.tsx\nsrc/.umi/exports.ts:9:51: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd\\index.tsx\nsrc/.umi/exports.ts:11:76: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-request\\index.ts\nsrc/.umi/exports.ts:27:28: ERROR: Could not resolve \"./testBrowser\"\n..."}
{"level":50,"time":1753949828691,"pid":10120,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 8 errors:\nsrc/.umi/core/plugin.ts:7:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-access/runtime.tsx\"\nsrc/.umi/core/plugin.ts:8:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-antd/runtime.tsx\"\nsrc/.umi/core/plugin.ts:9:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-initialState/runtime.tsx\"\nsrc/.umi/core/plugin.ts:10:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/runtime.tsx\"\nsrc/.umi/core/plugin.ts:11:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-model/runtime.tsx\"\n..."}
{"level":50,"time":1753949829608,"pid":10120,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/.umi/core/plugin.ts:11:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-model/runtime.tsx\"\nsrc/.umi/plugin-initialState/Provider.tsx:5:25: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-model\\index.tsx\nsrc/.umi/umi.ts:13:33: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\exports.ts\nsrc/.umi/umi.ts:78:7: ERROR: Could not resolve \"./plugin-moment2dayjs/runtime.tsx\""}
{"level":30,"time":1753949827224,"pid":2672,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] CSS 不够灵活、编写动态样式太繁琐怎么办？试试 styled-components 插件，详见 https://umijs.org/docs/max/styled-components\u001b[39m"}
{"level":30,"time":1753949827231,"pid":2672,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949827232,"pid":2672,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753949827234,"pid":2672,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753949829655,"pid":2672,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753950055354,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1753950055359,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753950055360,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753950055361,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753950057385,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753950161052,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753950161597,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1753950160900,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1753950160905,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753950160905,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753950160907,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753950162957,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753950163126,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753950163471,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 16 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753950446705,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/test-interceptor.tsx:14:8: ERROR: The symbol \"messageApi\" has already been declared"}
{"level":50,"time":1753950446715,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/test-interceptor.tsx:14:8: ERROR: The symbol \"messageApi\" has already been declared"}
{"level":50,"time":1753950485675,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/test-interceptor.tsx:14:8: ERROR: The symbol \"messageApi\" has already been declared"}
{"level":50,"time":1753950485700,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/test-interceptor.tsx:14:8: ERROR: The symbol \"messageApi\" has already been declared"}
{"level":50,"time":1753950530418,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/test-interceptor.tsx:14:8: ERROR: The symbol \"messageApi\" has already been declared"}
{"level":50,"time":1753950530392,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/test-interceptor.tsx:14:8: ERROR: The symbol \"messageApi\" has already been declared"}
{"level":50,"time":1753950568049,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753950568059,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753950568663,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1753950568667,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1753950570351,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753950570365,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753950570679,"pid":1628,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 17 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753950570697,"pid":14144,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 17 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1753950567861,"pid":21140,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想点击组件跳转至编辑器源码位置，可尝试新出的 clickToComponent 配置项，详见 https://umijs.org/docs/api/config#clicktocomponent\u001b[39m"}
{"level":30,"time":1753950567866,"pid":21140,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753950567867,"pid":21140,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753950567869,"pid":21140,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753950570069,"pid":21140,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":60,"time":1753950570433,"pid":21140,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Build failed with 1 error:\nsrc/pages/test-interceptor.tsx:14:8: ERROR: The symbol \"messageApi\" has already been declared","stack":"Error: Build failed with 1 error:\nsrc/pages/test-interceptor.tsx:14:8: ERROR: The symbol \"messageApi\" has already been declared\n    at failureErrorWithLog (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":8,"file":"src/pages/test-interceptor.tsx","length":0,"line":14,"lineText":"  const { message: messageApi } = App.useApp();","namespace":"","suggestion":""},"notes":[{"location":{"column":8,"file":"src/pages/test-interceptor.tsx","length":0,"line":13,"lineText":"  const { message: messageApi } = App.useApp();","namespace":"","suggestion":""},"text":"The symbol \"messageApi\" was originally declared here:"}],"pluginName":"","text":"The symbol \"messageApi\" has already been declared"}],"warnings":[]},"msg":"Build failed with 1 error:\nsrc/pages/test-interceptor.tsx:14:8: ERROR: The symbol \"messageApi\" has already been declared"}
{"level":60,"time":1753950570987,"pid":21140,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753950570999,"pid":21140,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753950571001,"pid":21140,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1753950753367,"pid":12544,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想点击组件跳转至编辑器源码位置，可尝试新出的 clickToComponent 配置项，详见 https://umijs.org/docs/api/config#clicktocomponent\u001b[39m"}
{"level":30,"time":1753950753372,"pid":12544,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753950753373,"pid":12544,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753950753375,"pid":12544,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753950755380,"pid":12544,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753951339233,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想点击组件跳转至编辑器源码位置，可尝试新出的 clickToComponent 配置项，详见 https://umijs.org/docs/api/config#clicktocomponent\u001b[39m"}
{"level":30,"time":1753951339242,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753951339243,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753951339248,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753951341266,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753951649609,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753951649734,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753951650249,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1753951651792,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753951652175,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1753951649477,"pid":21616,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局样式、全局脚本写在哪里？创建 src/global.(ts|css) 轻松解决，详见 https://umijs.org/docs/guides/directory-structure#globaljtsx\u001b[39m"}
{"level":30,"time":1753951649482,"pid":21616,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753951649482,"pid":21616,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753951649484,"pid":21616,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753951651698,"pid":21616,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753951739146,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/components/MessageProvider' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages/test-interceptor.tsx' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'"}
{"level":50,"time":1753951739120,"pid":21616,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/components/MessageProvider' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages/test-interceptor.tsx' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'"}
{"level":50,"time":1753951739711,"pid":21616,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages/test-interceptor.tsx' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'"}
{"level":60,"time":1753951743679,"pid":22396,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'","stack":"Error: Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'\n    at Function.resolveSync [as sync] (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\utils\\compiled\\resolve\\index.js:1:12304)\n    at Object.onResolveComponent (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:71:32)\n    at transformRoute (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:72:44)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:48:5\n    at Array.forEach (<anonymous>)\n    at transformRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:47:15)\n    at getConfigRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:38:3)\n    at Proxy.getRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:61:46)\n    at Hook.fn (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\appData\\appData.js:52:35)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\service\\service.js:136:38","code":"MODULE_NOT_FOUND"},"msg":"Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'"}
{"level":60,"time":1753951743951,"pid":22396,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753951743959,"pid":22396,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753951743976,"pid":22396,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":60,"time":1753952512271,"pid":16532,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'","stack":"Error: Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'\n    at Function.resolveSync [as sync] (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\utils\\compiled\\resolve\\index.js:1:12304)\n    at Object.onResolveComponent (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:71:32)\n    at transformRoute (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:72:44)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:48:5\n    at Array.forEach (<anonymous>)\n    at transformRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:47:15)\n    at getConfigRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:38:3)\n    at Proxy.getRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:61:46)\n    at Hook.fn (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\appData\\appData.js:52:35)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\service\\service.js:136:38","code":"MODULE_NOT_FOUND"},"msg":"Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'"}
{"level":60,"time":1753952512657,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753952512671,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753952512676,"pid":16532,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":60,"time":1753952599165,"pid":14276,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Cannot find module './team/detail' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'","stack":"Error: Cannot find module './team/detail' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'\n    at Function.resolveSync [as sync] (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\utils\\compiled\\resolve\\index.js:1:12304)\n    at Object.onResolveComponent (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:71:32)\n    at transformRoute (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:72:44)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:48:5\n    at Array.forEach (<anonymous>)\n    at transformRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:47:15)\n    at transformRoute (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:104:5)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:48:5\n    at Array.forEach (<anonymous>)\n    at transformRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:47:15)","code":"MODULE_NOT_FOUND"},"msg":"Cannot find module './team/detail' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'"}
{"level":60,"time":1753952599373,"pid":14276,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753952599388,"pid":14276,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753952599404,"pid":14276,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":60,"time":1753952641159,"pid":11688,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Cannot find module './user' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'","stack":"Error: Cannot find module './user' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'\n    at Function.resolveSync [as sync] (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\utils\\compiled\\resolve\\index.js:1:12304)\n    at Object.onResolveComponent (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:71:32)\n    at transformRoute (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:72:44)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:48:5\n    at Array.forEach (<anonymous>)\n    at transformRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:47:15)\n    at getConfigRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:38:3)\n    at Proxy.getRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:61:46)\n    at Hook.fn (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\appData\\appData.js:52:35)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\service\\service.js:136:38","code":"MODULE_NOT_FOUND"},"msg":"Cannot find module './user' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'"}
{"level":60,"time":1753952641560,"pid":11688,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753952641574,"pid":11688,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753952641589,"pid":11688,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":60,"time":1753952688827,"pid":8472,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Cannot find module './subscription' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'","stack":"Error: Cannot find module './subscription' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'\n    at Function.resolveSync [as sync] (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\utils\\compiled\\resolve\\index.js:1:12304)\n    at Object.onResolveComponent (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:71:32)\n    at transformRoute (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:72:44)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:48:5\n    at Array.forEach (<anonymous>)\n    at transformRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:47:15)\n    at getConfigRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:38:3)\n    at Proxy.getRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:61:46)\n    at Hook.fn (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\appData\\appData.js:52:35)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\service\\service.js:136:38","code":"MODULE_NOT_FOUND"},"msg":"Cannot find module './subscription' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'"}
{"level":60,"time":1753952689318,"pid":8472,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753952689325,"pid":8472,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753952689341,"pid":8472,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1753952733798,"pid":19004,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 遇到难解的配置问题，试试从 Umi FAQ 中寻找答案，详见 https://umijs.org/docs/introduce/faq\u001b[39m"}
{"level":30,"time":1753952733803,"pid":19004,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753952733803,"pid":19004,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753952733805,"pid":19004,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753952735816,"pid":19004,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753952894994,"pid":10972,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1753952894998,"pid":10972,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753952894998,"pid":10972,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753952895000,"pid":10972,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753952897025,"pid":10972,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753952990806,"pid":2336,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] father 4 正式发布了，详见 https://zhuanlan.zhihu.com/p/558192063\u001b[39m"}
{"level":30,"time":1753952990812,"pid":2336,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753952990812,"pid":2336,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753952990814,"pid":2336,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753952993086,"pid":2336,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753953183255,"pid":9568,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局样式、全局脚本写在哪里？创建 src/global.(ts|css) 轻松解决，详见 https://umijs.org/docs/guides/directory-structure#globaljtsx\u001b[39m"}
{"level":30,"time":1753953183259,"pid":9568,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753953183259,"pid":9568,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753953183261,"pid":9568,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753953185289,"pid":9568,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753953399233,"pid":22316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 编写 src/loading.(jsx|tsx) 可以自定义页面的加载动画。\u001b[39m"}
{"level":30,"time":1753953399237,"pid":22316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753953399238,"pid":22316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753953399239,"pid":22316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753953401386,"pid":22316,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753953599062,"pid":1384,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 遇到难解的配置问题，试试从 Umi FAQ 中寻找答案，详见 https://umijs.org/docs/introduce/faq\u001b[39m"}
{"level":30,"time":1753953599067,"pid":1384,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753953599067,"pid":1384,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753953599069,"pid":1384,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753953600954,"pid":1384,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753953640558,"pid":1384,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages/test-interceptor.tsx' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'"}
{"level":50,"time":1753953640813,"pid":1384,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages/test-interceptor.tsx' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'"}
{"level":60,"time":1753953644863,"pid":20236,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'","stack":"Error: Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'\n    at Function.resolveSync [as sync] (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\utils\\compiled\\resolve\\index.js:1:12304)\n    at Object.onResolveComponent (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:71:32)\n    at transformRoute (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:72:44)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:48:5\n    at Array.forEach (<anonymous>)\n    at transformRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:47:15)\n    at getConfigRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:38:3)\n    at Proxy.getRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:61:46)\n    at Hook.fn (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\appData\\appData.js:52:35)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\service\\service.js:136:38","code":"MODULE_NOT_FOUND"},"msg":"Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'"}
{"level":60,"time":1753953645085,"pid":20236,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753953645097,"pid":20236,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753953645113,"pid":20236,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":60,"time":1753953712825,"pid":19036,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'","stack":"Error: Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'\n    at Function.resolveSync [as sync] (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\utils\\compiled\\resolve\\index.js:1:12304)\n    at Object.onResolveComponent (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:71:32)\n    at transformRoute (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:72:44)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:48:5\n    at Array.forEach (<anonymous>)\n    at transformRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:47:15)\n    at getConfigRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\route\\routesConfig.js:38:3)\n    at Proxy.getRoutes (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\tmpFiles\\routes.js:61:46)\n    at Hook.fn (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\preset-umi\\dist\\features\\appData\\appData.js:52:35)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\core\\dist\\service\\service.js:136:38","code":"MODULE_NOT_FOUND"},"msg":"Cannot find module './team' from 'H:/projects/IdeaProjects/teamAuth/frontend/src/pages'"}
{"level":60,"time":1753953713009,"pid":19036,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1753953713010,"pid":19036,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1753953713021,"pid":19036,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1753953947193,"pid":12576,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要增加 Git 提交消息校验和自动代码格式化, max g precommit 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#precommit-配置生成器\u001b[39m"}
{"level":30,"time":1753953947198,"pid":12576,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753953947199,"pid":12576,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753953947201,"pid":12576,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753953949285,"pid":12576,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753954907096,"pid":18176,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g page 可以快速生成页面模板，详见 https://umijs.org/docs/guides/generator#页面生成器\u001b[39m"}
{"level":30,"time":1753954907105,"pid":18176,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753954907108,"pid":18176,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753954907111,"pid":18176,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753954909039,"pid":18176,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753955114157,"pid":18684,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] father 4 正式发布了，详见 https://zhuanlan.zhihu.com/p/558192063\u001b[39m"}
{"level":30,"time":1753955114162,"pid":18684,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955114163,"pid":18684,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955114165,"pid":18684,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753955116110,"pid":18684,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753955484789,"pid":7316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] dev 模式下访问 /__umi 路由，可以发现很多有用的内部信息。\u001b[39m"}
{"level":30,"time":1753955484794,"pid":7316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955484794,"pid":7316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955484797,"pid":7316,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753955486874,"pid":7316,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753955730667,"pid":7316,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753955731209,"pid":7316,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1753955732805,"pid":7316,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753955733209,"pid":7316,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1753955730509,"pid":6468,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1753955730514,"pid":6468,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955730514,"pid":6468,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955730518,"pid":6468,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753955732673,"pid":6468,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753955963442,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] ANALYZE=1 max build 可以分析产物的源码构成。\u001b[39m"}
{"level":30,"time":1753955963447,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955963448,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955963450,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753955965504,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753955967372,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 12 errors:\nsrc/.umi/exports.ts:5:26: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core\\defineApp.ts\nsrc/.umi/exports.ts:8:57: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-access\\index.tsx\nsrc/.umi/exports.ts:9:51: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd\\index.tsx\nsrc/.umi/exports.ts:11:76: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-request\\index.ts\nsrc/.umi/exports.ts:27:28: ERROR: Could not resolve \"./testBrowser\"\n..."}
{"level":50,"time":1753955967549,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 8 errors:\nsrc/.umi/core/plugin.ts:7:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-access/runtime.tsx\"\nsrc/.umi/core/plugin.ts:8:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-antd/runtime.tsx\"\nsrc/.umi/core/plugin.ts:9:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-initialState/runtime.tsx\"\nsrc/.umi/core/plugin.ts:10:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-layout/runtime.tsx\"\nsrc/.umi/core/plugin.ts:11:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-model/runtime.tsx\"\n..."}
{"level":50,"time":1753955968757,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/.umi/core/plugin.ts:11:26: ERROR: Could not resolve \"H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-model/runtime.tsx\"\nsrc/.umi/plugin-access/runtime.tsx:6:25: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-model\\index.tsx\nsrc/.umi/umi.ts:13:33: ERROR: Could not read from file: H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\exports.ts\nsrc/.umi/umi.ts:78:7: ERROR: Could not resolve \"./plugin-moment2dayjs/runtime.tsx\""}
{"level":30,"time":1753955966068,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] ANALYZE=1 max build 可以分析产物的源码构成。\u001b[39m"}
{"level":30,"time":1753955966073,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955966074,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753955966076,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753955968667,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753955975487,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753955975532,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753956011270,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753956011252,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753956029540,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753956029581,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753956038608,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753956038616,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753956049495,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753956049512,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/MessageProvider/index.tsx:3:9: ERROR: No matching export in \"src/utils/request.ts\" for import \"setMessageApi\""}
{"level":50,"time":1753956064663,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/index.ts:16:28: ERROR: Could not resolve \"./MessageProvider\""}
{"level":50,"time":1753956064692,"pid":12448,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/components/index.ts:16:28: ERROR: Could not resolve \"./MessageProvider\""}
{"level":50,"time":1753956141196,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 6 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\nsrc/.umi/umi.ts:11:30: ERROR: Could not resolve \"./core/history\"\n..."}
{"level":50,"time":1753956141313,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753956141619,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1753956141028,"pid":12888,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] @umijs/max 和 umi 如何选择？max 内置了很多好用的插件，即开即用，而 umi 也可以手动配置插件，独立使用，详见 https://umijs.org/docs/guides/use-plugins\u001b[39m"}
{"level":30,"time":1753956141033,"pid":12888,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956141033,"pid":12888,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956141035,"pid":12888,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753956143041,"pid":12888,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753956143310,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 16 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753956206114,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753956206228,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753956206683,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1753956206028,"pid":2188,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 页面加载慢、产物体积大怎么办？试试做代码拆分，详见 https://umijs.org/blog/code-splitting\u001b[39m"}
{"level":30,"time":1753956206032,"pid":2188,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956206033,"pid":2188,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956206034,"pid":2188,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753956208010,"pid":2188,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753956208164,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 17 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753956208471,"pid":20644,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 16 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1753956290280,"pid":4932,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1753956290285,"pid":4932,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956290286,"pid":4932,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956290288,"pid":4932,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753956292323,"pid":4932,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753956378616,"pid":4012,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 需要添加全局 React Context 吗？在 src/app.(ts|tsx) 运行时配置中轻松解决，详见 https://umijs.org/docs/api/runtime-config\u001b[39m"}
{"level":30,"time":1753956378622,"pid":4012,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956378623,"pid":4012,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956378625,"pid":4012,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753956380567,"pid":4012,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753956558152,"pid":11392,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1753956558157,"pid":11392,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956558157,"pid":11392,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956558160,"pid":11392,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753956560177,"pid":11392,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1753956596225,"pid":1320,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1753956596230,"pid":1320,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956596231,"pid":1320,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753956596232,"pid":1320,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753956598296,"pid":1320,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753957023190,"pid":1320,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/components/index.ts:16:28: ERROR: Could not resolve \"./MessageProvider\"\nsrc/utils/request.ts:9:24: ERROR: Could not resolve \"./notification\""}
{"level":30,"time":1753957075968,"pid":14980,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 遇到难解的配置问题，试试从 Umi FAQ 中寻找答案，详见 https://umijs.org/docs/introduce/faq\u001b[39m"}
{"level":30,"time":1753957075973,"pid":14980,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753957075974,"pid":14980,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753957075976,"pid":14980,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753957077999,"pid":14980,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753957309364,"pid":14980,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1753957309936,"pid":14980,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1753957309214,"pid":17512,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 请求加载态、数据管理、避免竟态问题，用 react-query 帮你全部解决，详见 https://umijs.org/docs/max/react-query\u001b[39m"}
{"level":30,"time":1753957309219,"pid":17512,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753957309219,"pid":17512,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1753957309221,"pid":17512,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1753957311353,"pid":17512,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1753957311480,"pid":14980,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1753957311900,"pid":14980,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 16 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754012085443,"pid":9432,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局样式、全局脚本写在哪里？创建 src/global.(ts|css) 轻松解决，详见 https://umijs.org/docs/guides/directory-structure#globaljtsx\u001b[39m"}
{"level":30,"time":1754012085465,"pid":9432,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754012085466,"pid":9432,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754012085469,"pid":9432,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754012089544,"pid":9432,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754012287665,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1754012287672,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754012287673,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754012287675,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754012289783,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":32,"time":1754014574444,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":50,"time":1754014669373,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 6 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\nsrc/.umi/umi.ts:11:30: ERROR: Could not resolve \"./core/history\"\n..."}
{"level":50,"time":1754014669485,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754014670011,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754014671757,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754014672197,"pid":6320,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754014669105,"pid":16664,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 页面加载慢、产物体积大怎么办？试试做代码拆分，详见 https://umijs.org/blog/code-splitting\u001b[39m"}
{"level":30,"time":1754014669110,"pid":16664,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754014669110,"pid":16664,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754014669112,"pid":16664,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754014671696,"pid":16664,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754016490134,"pid":23128,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1754016490145,"pid":23128,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754016490145,"pid":23128,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754016490146,"pid":23128,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754016491945,"pid":23128,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754016976757,"pid":23128,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754016976877,"pid":23128,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754016977412,"pid":23128,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754016976645,"pid":16808,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 需要添加全局 React Context 吗？在 src/app.(ts|tsx) 运行时配置中轻松解决，详见 https://umijs.org/docs/api/runtime-config\u001b[39m"}
{"level":30,"time":1754016976651,"pid":16808,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754016976651,"pid":16808,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754016976653,"pid":16808,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754016978995,"pid":16808,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754016979282,"pid":23128,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754017029269,"pid":17088,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g tsconfig 可一键完成项目的 TypeScript 配置。\u001b[39m"}
{"level":30,"time":1754017029275,"pid":17088,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754017029276,"pid":17088,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754017029278,"pid":17088,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754017031365,"pid":17088,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754018614317,"pid":3924,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] @umijs/max 是蚂蚁内网框架 Bigfish 的对外版本。\u001b[39m"}
{"level":30,"time":1754018614322,"pid":3924,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754018614323,"pid":3924,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754018614325,"pid":3924,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754018616298,"pid":3924,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754018820544,"pid":2444,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 遇到难解的配置问题，试试从 Umi FAQ 中寻找答案，详见 https://umijs.org/docs/introduce/faq\u001b[39m"}
{"level":30,"time":1754018820549,"pid":2444,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754018820550,"pid":2444,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754018820552,"pid":2444,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754018822538,"pid":2444,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754019534727,"pid":2444,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":30,"time":1754019534588,"pid":13372,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g tsconfig 可一键完成项目的 TypeScript 配置。\u001b[39m"}
{"level":30,"time":1754019534592,"pid":13372,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754019534593,"pid":13372,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754019534595,"pid":13372,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754019536633,"pid":13372,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754020636312,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 编写 src/loading.(jsx|tsx) 可以自定义页面的加载动画。\u001b[39m"}
{"level":30,"time":1754020636318,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754020636319,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754020636320,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754020638231,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754020655020,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Memory Usage: 140.97 MB (RSS: 586.45 MB)"}
{"level":32,"time":1754020655089,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\index.html"}
{"level":32,"time":1754020655091,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\login\\index.html"}
{"level":32,"time":1754020655093,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Build dashboard\\index.html"}
{"level":32,"time":1754020655095,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Build team-management\\index.html"}
{"level":32,"time":1754020655100,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Build personal-center\\index.html"}
{"level":32,"time":1754020655103,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Build settings\\index.html"}
{"level":32,"time":1754020655107,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Build help\\index.html"}
{"level":32,"time":1754020655112,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Build index.html"}
{"level":32,"time":1754020655120,"pid":17096,"hostname":"DESKTOP-N6C2H1N","msg":"Build 404.html"}
{"level":30,"time":1754020688944,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1754020688949,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754020688949,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754020688951,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754020690978,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754024086151,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754024086749,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754024085978,"pid":5428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 页面加载慢、产物体积大怎么办？试试做代码拆分，详见 https://umijs.org/blog/code-splitting\u001b[39m"}
{"level":30,"time":1754024085982,"pid":5428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754024085983,"pid":5428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754024085984,"pid":5428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754024088142,"pid":5428,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754024088324,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754024088666,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754024404375,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:9:26: ERROR: Could not resolve \"./LastLoginInfo\""}
{"level":50,"time":1754024404508,"pid":5428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/pages/personal-center/index.tsx:9:26: ERROR: Could not resolve \"./LastLoginInfo\""}
{"level":50,"time":1754024526238,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754024526829,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754024528482,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754024528847,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754024526085,"pid":20468,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] father 4 正式发布了，详见 https://zhuanlan.zhihu.com/p/558192063\u001b[39m"}
{"level":30,"time":1754024526089,"pid":20468,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754024526089,"pid":20468,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754024526091,"pid":20468,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754024528370,"pid":20468,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754026016253,"pid":20468,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:26:28: ERROR: Could not resolve \"./CreateTeamModal\"\nsrc/pages/personal-center/PersonalInfo.tsx:27:34: ERROR: Could not resolve \"./PersonalSettingsModal\""}
{"level":50,"time":1754026016148,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:26:28: ERROR: Could not resolve \"./CreateTeamModal\"\nsrc/pages/personal-center/PersonalInfo.tsx:27:34: ERROR: Could not resolve \"./PersonalSettingsModal\""}
{"level":50,"time":1754026033539,"pid":20468,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:25:28: ERROR: Could not resolve \"./CreateTeamModal\"\nsrc/pages/personal-center/PersonalInfo.tsx:26:34: ERROR: Could not resolve \"./PersonalSettingsModal\""}
{"level":50,"time":1754026033522,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:25:28: ERROR: Could not resolve \"./CreateTeamModal\"\nsrc/pages/personal-center/PersonalInfo.tsx:26:34: ERROR: Could not resolve \"./PersonalSettingsModal\""}
{"level":50,"time":1754026091115,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 6 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\nsrc/.umi/umi.ts:11:30: ERROR: Could not resolve \"./core/history\"\n..."}
{"level":50,"time":1754026091232,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754026091572,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754026090921,"pid":19864,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Tailwind CSS, max g tailwindcss 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#tailwind-css-配置生成器\u001b[39m"}
{"level":30,"time":1754026090926,"pid":19864,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754026090927,"pid":19864,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754026090930,"pid":19864,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754026093022,"pid":19864,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754026093179,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754026093580,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754026417871,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 6 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\nsrc/.umi/umi.ts:11:30: ERROR: Could not resolve \"./core/history\"\n..."}
{"level":50,"time":1754026417989,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754026418367,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754026419945,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754026420328,"pid":10264,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754026417685,"pid":13504,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1754026417690,"pid":13504,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754026417690,"pid":13504,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754026417692,"pid":13504,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754026419784,"pid":13504,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754026790401,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想点击组件跳转至编辑器源码位置，可尝试新出的 clickToComponent 配置项，详见 https://umijs.org/docs/api/config#clicktocomponent\u001b[39m"}
{"level":30,"time":1754026790405,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754026790406,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754026790408,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754026792493,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754027478761,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/TeamListCard.tsx:1133:22: ERROR: Unexpected closing \"Card\" tag does not match opening \"ProList\" tag\nsrc/pages/personal-center/TeamListCard.tsx:1134:30: ERROR: Unterminated regular expression"}
{"level":50,"time":1754027701325,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754027701898,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754027701174,"pid":20120,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g page 可以快速生成页面模板，详见 https://umijs.org/docs/guides/generator#页面生成器\u001b[39m"}
{"level":30,"time":1754027701179,"pid":20120,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754027701180,"pid":20120,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754027701183,"pid":20120,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754027703213,"pid":20120,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754027703421,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754027703787,"pid":21428,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754027724894,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Prettier, max g prettier 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#prettier-配置生成器\u001b[39m"}
{"level":30,"time":1754027724900,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754027724900,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754027724902,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754027726742,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754027737489,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Memory Usage: 141.37 MB (RSS: 650.83 MB)"}
{"level":32,"time":1754027737559,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\index.html"}
{"level":32,"time":1754027737561,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Build user\\login\\index.html"}
{"level":32,"time":1754027737562,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Build dashboard\\index.html"}
{"level":32,"time":1754027737567,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Build team-management\\index.html"}
{"level":32,"time":1754027737569,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Build personal-center\\index.html"}
{"level":32,"time":1754027737574,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Build settings\\index.html"}
{"level":32,"time":1754027737577,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Build help\\index.html"}
{"level":32,"time":1754027737590,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Build index.html"}
{"level":32,"time":1754027737594,"pid":9876,"hostname":"DESKTOP-N6C2H1N","msg":"Build 404.html"}
{"level":50,"time":1754027784783,"pid":20120,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754027785368,"pid":20120,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754027784620,"pid":20584,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] COMPRESS=none max build 可以关闭项目构建时的代码压缩功能, 方便调试项目的构建产物。\u001b[39m"}
{"level":30,"time":1754027784624,"pid":20584,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754027784625,"pid":20584,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754027784626,"pid":20584,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754027786633,"pid":20584,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754027786940,"pid":20120,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754028102877,"pid":24368,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] @umijs/max 和 umi 如何选择？max 内置了很多好用的插件，即开即用，而 umi 也可以手动配置插件，独立使用，详见 https://umijs.org/docs/guides/use-plugins\u001b[39m"}
{"level":30,"time":1754028102881,"pid":24368,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754028102883,"pid":24368,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754028102886,"pid":24368,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754028104970,"pid":24368,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754030209669,"pid":8104,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] PORT=9000 max dev 可以指定 Umi 开发服务器的端口。\u001b[39m"}
{"level":30,"time":1754030209673,"pid":8104,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754030209674,"pid":8104,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754030209675,"pid":8104,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754030211682,"pid":8104,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754032056650,"pid":8104,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754032057140,"pid":8104,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754032056507,"pid":8976,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] COMPRESS=none max build 可以关闭项目构建时的代码压缩功能, 方便调试项目的构建产物。\u001b[39m"}
{"level":30,"time":1754032056512,"pid":8976,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754032056513,"pid":8976,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754032056515,"pid":8976,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754032058518,"pid":8976,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754032058645,"pid":8104,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754032059094,"pid":8104,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754032799398,"pid":20736,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想预览构建后产物, 可尝试 max preview，详见 https://umijs.org/docs/api/commands#preview\u001b[39m"}
{"level":30,"time":1754032799405,"pid":20736,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754032799406,"pid":20736,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754032799408,"pid":20736,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754032801579,"pid":20736,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":32,"time":1754033993864,"pid":20736,"hostname":"DESKTOP-N6C2H1N","msg":"config routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":50,"time":1754034558396,"pid":20736,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 2 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:251:16: ERROR: Unexpected closing \"Col\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:252:18: ERROR: Unterminated regular expression"}
{"level":30,"time":1754034779365,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] COMPRESS=none max build 可以关闭项目构建时的代码压缩功能, 方便调试项目的构建产物。\u001b[39m"}
{"level":30,"time":1754034779369,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754034779370,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754034779372,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754034781857,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754036033105,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 5 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:10: ERROR: Unexpected closing \"Spin\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:7: ERROR: The character \"}\" is not valid inside a JSX element\nsrc/pages/personal-center/PersonalInfo.tsx:288:6: ERROR: Unexpected closing \"ProCard\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:290:0: ERROR: The character \"}\" is not valid inside a JSX element\nsrc/pages/personal-center/PersonalInfo.tsx:293:0: ERROR: Unexpected end of file before a closing \"Spin\" tag"}
{"level":50,"time":1754036072142,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":50,"time":1754036083963,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":50,"time":1754036097465,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":50,"time":1754036401861,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":50,"time":1754036414011,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":50,"time":1754036491286,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 6 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\nsrc/.umi/umi.ts:11:30: ERROR: Could not resolve \"./core/history\"\n..."}
{"level":50,"time":1754036491402,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754036491744,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 26 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754036491111,"pid":24524,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 编写 src/loading.(jsx|tsx) 可以自定义页面的加载动画。\u001b[39m"}
{"level":30,"time":1754036491116,"pid":24524,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754036491116,"pid":24524,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754036491118,"pid":24524,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754036493123,"pid":24524,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":60,"time":1754036493523,"pid":24524,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression","stack":"Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression\n    at failureErrorWithLog (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":16,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":275,"lineText":"              </div>","namespace":"","suggestion":"Col"},"notes":[{"location":{"column":13,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":152,"lineText":"            <Col xs={24} sm={24} md={14} lg={14} xl={14}>","namespace":"","suggestion":""},"text":"The opening \"Col\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"div\" tag does not match opening \"Col\" tag"},{"id":"","location":{"column":14,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":276,"lineText":"            </Col>","namespace":"","suggestion":"Row"},"notes":[{"location":{"column":11,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":150,"lineText":"          <Row gutter={[24, 16]}>","namespace":"","suggestion":""},"text":"The opening \"Row\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Col\" tag does not match opening \"Row\" tag"},{"id":"","location":{"column":12,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":403,"lineText":"          </Row>","namespace":"","suggestion":"Spin"},"notes":[{"location":{"column":9,"file":"src/pages/personal-center/PersonalInfo.tsx","length":4,"line":148,"lineText":"        <Spin spinning={userInfoLoading || statsLoading}>","namespace":"","suggestion":""},"text":"The opening \"Spin\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Row\" tag does not match opening \"Spin\" tag"},{"id":"","location":{"column":15,"file":"src/pages/personal-center/PersonalInfo.tsx","length":0,"line":404,"lineText":"        </Spin>","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"Unterminated regular expression"}],"warnings":[]},"msg":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":60,"time":1754036494122,"pid":24524,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1754036494135,"pid":24524,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1754036494151,"pid":24524,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":50,"time":1754036493278,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754036493663,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754036690305,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":50,"time":1754036734366,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":50,"time":1754036750185,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":50,"time":1754036779677,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754036780265,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 26 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754036779482,"pid":7384,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1754036779487,"pid":7384,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754036779488,"pid":7384,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754036779492,"pid":7384,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754036781685,"pid":7384,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":60,"time":1754036782036,"pid":7384,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression","stack":"Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression\n    at failureErrorWithLog (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":16,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":275,"lineText":"              </div>","namespace":"","suggestion":"Col"},"notes":[{"location":{"column":13,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":152,"lineText":"            <Col xs={24} sm={24} md={14} lg={14} xl={14}>","namespace":"","suggestion":""},"text":"The opening \"Col\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"div\" tag does not match opening \"Col\" tag"},{"id":"","location":{"column":14,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":276,"lineText":"            </Col>","namespace":"","suggestion":"Row"},"notes":[{"location":{"column":11,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":150,"lineText":"          <Row gutter={[24, 16]}>","namespace":"","suggestion":""},"text":"The opening \"Row\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Col\" tag does not match opening \"Row\" tag"},{"id":"","location":{"column":12,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":403,"lineText":"          </Row>","namespace":"","suggestion":"Spin"},"notes":[{"location":{"column":9,"file":"src/pages/personal-center/PersonalInfo.tsx","length":4,"line":148,"lineText":"        <Spin spinning={userInfoLoading || statsLoading}>","namespace":"","suggestion":""},"text":"The opening \"Spin\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Row\" tag does not match opening \"Spin\" tag"},{"id":"","location":{"column":15,"file":"src/pages/personal-center/PersonalInfo.tsx","length":0,"line":404,"lineText":"        </Spin>","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"Unterminated regular expression"}],"warnings":[]},"msg":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":60,"time":1754036782064,"pid":7384,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1754036782079,"pid":7384,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1754036782094,"pid":7384,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":50,"time":1754036781904,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 27 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754036782256,"pid":11240,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 25 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754036847407,"pid":21764,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] CSS 不够灵活、编写动态样式太繁琐怎么办？试试 styled-components 插件，详见 https://umijs.org/docs/max/styled-components\u001b[39m"}
{"level":30,"time":1754036847411,"pid":21764,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754036847412,"pid":21764,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754036847413,"pid":21764,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754036849395,"pid":21764,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":60,"time":1754036849677,"pid":21764,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression","stack":"Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression\n    at failureErrorWithLog (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":16,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":275,"lineText":"              </div>","namespace":"","suggestion":"Col"},"notes":[{"location":{"column":13,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":152,"lineText":"            <Col xs={24} sm={24} md={14} lg={14} xl={14}>","namespace":"","suggestion":""},"text":"The opening \"Col\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"div\" tag does not match opening \"Col\" tag"},{"id":"","location":{"column":14,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":276,"lineText":"            </Col>","namespace":"","suggestion":"Row"},"notes":[{"location":{"column":11,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":150,"lineText":"          <Row gutter={[24, 16]}>","namespace":"","suggestion":""},"text":"The opening \"Row\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Col\" tag does not match opening \"Row\" tag"},{"id":"","location":{"column":12,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":403,"lineText":"          </Row>","namespace":"","suggestion":"Spin"},"notes":[{"location":{"column":9,"file":"src/pages/personal-center/PersonalInfo.tsx","length":4,"line":148,"lineText":"        <Spin spinning={userInfoLoading || statsLoading}>","namespace":"","suggestion":""},"text":"The opening \"Spin\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Row\" tag does not match opening \"Spin\" tag"},{"id":"","location":{"column":15,"file":"src/pages/personal-center/PersonalInfo.tsx","length":0,"line":404,"lineText":"        </Spin>","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"Unterminated regular expression"}],"warnings":[]},"msg":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":60,"time":1754036849693,"pid":21764,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1754036849705,"pid":21764,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1754036849720,"pid":21764,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1754036927725,"pid":3992,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1754036927730,"pid":3992,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754036927730,"pid":3992,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754036927732,"pid":3992,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754036929679,"pid":3992,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":60,"time":1754036930019,"pid":3992,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression","stack":"Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression\n    at failureErrorWithLog (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":16,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":275,"lineText":"              </div>","namespace":"","suggestion":"Col"},"notes":[{"location":{"column":13,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":152,"lineText":"            <Col xs={24} sm={24} md={14} lg={14} xl={14}>","namespace":"","suggestion":""},"text":"The opening \"Col\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"div\" tag does not match opening \"Col\" tag"},{"id":"","location":{"column":14,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":276,"lineText":"            </Col>","namespace":"","suggestion":"Row"},"notes":[{"location":{"column":11,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":150,"lineText":"          <Row gutter={[24, 16]}>","namespace":"","suggestion":""},"text":"The opening \"Row\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Col\" tag does not match opening \"Row\" tag"},{"id":"","location":{"column":12,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":403,"lineText":"          </Row>","namespace":"","suggestion":"Spin"},"notes":[{"location":{"column":9,"file":"src/pages/personal-center/PersonalInfo.tsx","length":4,"line":148,"lineText":"        <Spin spinning={userInfoLoading || statsLoading}>","namespace":"","suggestion":""},"text":"The opening \"Spin\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Row\" tag does not match opening \"Spin\" tag"},{"id":"","location":{"column":15,"file":"src/pages/personal-center/PersonalInfo.tsx","length":0,"line":404,"lineText":"        </Spin>","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"Unterminated regular expression"}],"warnings":[]},"msg":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":60,"time":1754036930061,"pid":3992,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1754036930073,"pid":3992,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1754036930076,"pid":3992,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1754037082367,"pid":22752,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1754037082372,"pid":22752,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754037082373,"pid":22752,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754037082375,"pid":22752,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754037084269,"pid":22752,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754037191633,"pid":7264,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局布局用 layout ，多层布局用 wrappers ，从文档了解更多路由的控制方法，详见 https://umijs.org/docs/guides/routes\u001b[39m"}
{"level":30,"time":1754037191638,"pid":7264,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754037191639,"pid":7264,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754037191641,"pid":7264,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754037193610,"pid":7264,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":60,"time":1754037193912,"pid":7264,"hostname":"DESKTOP-N6C2H1N","err":{"type":"Error","message":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression","stack":"Error: Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression\n    at failureErrorWithLog (H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1472:15)\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:945:25\n    at H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\@umijs\\bundler-utils\\node_modules\\esbuild\\lib\\main.js:1353:9\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","errors":[{"id":"","location":{"column":16,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":275,"lineText":"              </div>","namespace":"","suggestion":"Col"},"notes":[{"location":{"column":13,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":152,"lineText":"            <Col xs={24} sm={24} md={14} lg={14} xl={14}>","namespace":"","suggestion":""},"text":"The opening \"Col\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"div\" tag does not match opening \"Col\" tag"},{"id":"","location":{"column":14,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":276,"lineText":"            </Col>","namespace":"","suggestion":"Row"},"notes":[{"location":{"column":11,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":150,"lineText":"          <Row gutter={[24, 16]}>","namespace":"","suggestion":""},"text":"The opening \"Row\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Col\" tag does not match opening \"Row\" tag"},{"id":"","location":{"column":12,"file":"src/pages/personal-center/PersonalInfo.tsx","length":3,"line":403,"lineText":"          </Row>","namespace":"","suggestion":"Spin"},"notes":[{"location":{"column":9,"file":"src/pages/personal-center/PersonalInfo.tsx","length":4,"line":148,"lineText":"        <Spin spinning={userInfoLoading || statsLoading}>","namespace":"","suggestion":""},"text":"The opening \"Spin\" tag is here:"}],"pluginName":"","text":"Unexpected closing \"Row\" tag does not match opening \"Spin\" tag"},{"id":"","location":{"column":15,"file":"src/pages/personal-center/PersonalInfo.tsx","length":0,"line":404,"lineText":"        </Spin>","namespace":"","suggestion":""},"notes":[],"pluginName":"","text":"Unterminated regular expression"}],"warnings":[]},"msg":"Build failed with 4 errors:\nsrc/pages/personal-center/PersonalInfo.tsx:275:16: ERROR: Unexpected closing \"div\" tag does not match opening \"Col\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:276:14: ERROR: Unexpected closing \"Col\" tag does not match opening \"Row\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:403:12: ERROR: Unexpected closing \"Row\" tag does not match opening \"Spin\" tag\nsrc/pages/personal-center/PersonalInfo.tsx:404:15: ERROR: Unterminated regular expression"}
{"level":60,"time":1754037194031,"pid":7264,"hostname":"DESKTOP-N6C2H1N","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1754037194043,"pid":7264,"hostname":"DESKTOP-N6C2H1N","msg":"H:\\projects\\IdeaProjects\\teamAuth\\frontend\\node_modules\\.cache\\logger\\umi.log"}
{"level":60,"time":1754037194059,"pid":7264,"hostname":"DESKTOP-N6C2H1N","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1754037236392,"pid":9280,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g page 可以快速生成页面模板，详见 https://umijs.org/docs/guides/generator#页面生成器\u001b[39m"}
{"level":30,"time":1754037236397,"pid":9280,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754037236398,"pid":9280,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754037236400,"pid":9280,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754037238430,"pid":9280,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754037969100,"pid":9280,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754037969677,"pid":9280,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754037971201,"pid":9280,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 20 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754037971551,"pid":9280,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 18 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754037968968,"pid":11280,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, max g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1754037968972,"pid":11280,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754037968972,"pid":11280,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754037968974,"pid":11280,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754037971086,"pid":11280,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754280601779,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1754280601828,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754280601829,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754280601831,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754280607881,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754291637833,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 6 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\nsrc/.umi/umi.ts:11:30: ERROR: Could not resolve \"./core/history\"\n..."}
{"level":50,"time":1754291637943,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754291638597,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754291637447,"pid":18896,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果你需要增加 Git 提交消息校验和自动代码格式化, max g precommit 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#precommit-配置生成器\u001b[39m"}
{"level":30,"time":1754291637486,"pid":18896,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754291637486,"pid":18896,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754291637488,"pid":18896,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754291641550,"pid":18896,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754291641641,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754291641867,"pid":15224,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 3 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-model' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-access'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-model' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-initialState'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/plugin-model' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-layout'"}
{"level":30,"time":1754292948828,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g page 可以快速生成页面模板，详见 https://umijs.org/docs/guides/generator#页面生成器\u001b[39m"}
{"level":30,"time":1754292948834,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754292948835,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754292948837,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754292951139,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754294541546,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 6 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\nsrc/.umi/umi.ts:11:30: ERROR: Could not resolve \"./core/history\"\n..."}
{"level":50,"time":1754294541660,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754294541977,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754294541332,"pid":23836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] HMR=none max dev 可以关闭 Umi 开发服务器的模块热替换功能。\u001b[39m"}
{"level":30,"time":1754294541338,"pid":23836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754294541339,"pid":23836,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754294541391,"pid":23836,"hostname":"DESKTOP-N6C2H1N","msg":"generate files"}
{"level":30,"time":1754294543380,"pid":23836,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754294543488,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754294543901,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754294634983,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 6 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\nsrc/.umi/umi.ts:11:30: ERROR: Could not resolve \"./core/history\"\n..."}
{"level":50,"time":1754294635096,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754294635527,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754294635861,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754294634808,"pid":10500,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1754294634812,"pid":10500,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754294634812,"pid":10500,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754294634815,"pid":10500,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754294636999,"pid":10500,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754294637133,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 21 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754294637551,"pid":19716,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754297221043,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1754297221049,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754297221050,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754297221052,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754297223125,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754299433840,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 6 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nsrc/.umi/umi.ts:4:7: ERROR: Could not resolve \"./core/polyfill\"\nsrc/.umi/umi.ts:9:26: ERROR: Could not resolve \"./core/route\"\nsrc/.umi/umi.ts:10:36: ERROR: Could not resolve \"./core/plugin\"\nsrc/.umi/umi.ts:11:30: ERROR: Could not resolve \"./core/history\"\n..."}
{"level":50,"time":1754299433951,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754299434314,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754299433635,"pid":11492,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] father 4 正式发布了，详见 https://zhuanlan.zhihu.com/p/558192063\u001b[39m"}
{"level":30,"time":1754299433639,"pid":11492,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754299433640,"pid":11492,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754299433642,"pid":11492,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754299435802,"pid":11492,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754299435915,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754299436355,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754299477896,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754299478348,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":50,"time":1754299480147,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 19 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754299477777,"pid":24552,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 如果想检测未使用的文件和导出，可尝试新出的 deadCode 配置项，详见 https://umijs.org/docs/api/config#deadcode\u001b[39m"}
{"level":30,"time":1754299477781,"pid":24552,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754299477782,"pid":24552,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754299477784,"pid":24552,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754299479790,"pid":24552,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754299702939,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 1 error:\nerror: Could not resolve \"H:\\\\projects\\\\IdeaProjects\\\\teamAuth\\\\frontend\\\\src\\\\.umi\\\\umi.ts\""}
{"level":50,"time":1754299703563,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 23 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\components\\ErrorBoundary'\n..."}
{"level":30,"time":1754299702770,"pid":22648,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 需要添加全局 React Context 吗？在 src/app.(ts|tsx) 运行时配置中轻松解决，详见 https://umijs.org/docs/api/runtime-config\u001b[39m"}
{"level":30,"time":1754299702783,"pid":22648,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754299702784,"pid":22648,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754299702785,"pid":22648,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754299705161,"pid":22648,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":50,"time":1754299705298,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 24 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":50,"time":1754299705693,"pid":17964,"hostname":"DESKTOP-N6C2H1N","msg":"[icons] build failed: Error: Build failed with 22 errors:\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\core'\nnode_modules/enhanced-resolve/lib/Resolver.js:309:17: ERROR: [plugin: esbuildAliasPlugin] Can't resolve 'H:/projects/IdeaProjects/teamAuth/frontend/src/.umi/exports' in 'H:\\projects\\IdeaProjects\\teamAuth\\frontend\\src\\.umi\\plugin-antd'\n..."}
{"level":30,"time":1754299982573,"pid":1860,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1754299982578,"pid":1860,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754299982579,"pid":1860,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754299982580,"pid":1860,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754299985943,"pid":1860,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754300046120,"pid":19932,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] max g component 可以快速生成组件模板，详见 https://umijs.org/docs/guides/generator#组件生成器\u001b[39m"}
{"level":30,"time":1754300046126,"pid":19932,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754300046126,"pid":19932,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754300046128,"pid":19932,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754300048269,"pid":19932,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754300548484,"pid":17972,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 默认使用 esbuild 作为 JavaScript 压缩工具，也可通过 jsMinifier 配置项切换到 terser 或 uglifyJs 等，详见 https://umijs.org/docs/api/config#jsminifier-webpack\u001b[39m"}
{"level":30,"time":1754300548489,"pid":17972,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754300548489,"pid":17972,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754300548491,"pid":17972,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754300550500,"pid":17972,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
{"level":30,"time":1754364684035,"pid":13236,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[33m[你知道吗？] 全局数据存储，在 React 之外修改数据怎么办？试试一键上手 valtio，详见 https://umijs.org/docs/max/valtio\u001b[39m"}
{"level":30,"time":1754364684058,"pid":13236,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754364684058,"pid":13236,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[32m[plugin: ./node_modules/@umijs/preset-umi/dist/features/mako/mako]\u001b[39m"}
{"level":30,"time":1754364684059,"pid":13236,"hostname":"DESKTOP-N6C2H1N","msg":"\u001b[36m\u001b[1mUmi v4.4.11\u001b[22m\u001b[39m"}
{"level":30,"time":1754364686459,"pid":13236,"hostname":"DESKTOP-N6C2H1N","msg":"Preparing..."}
